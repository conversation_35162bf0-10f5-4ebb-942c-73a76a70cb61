import React from 'react';
import {
  Box,
  Button,
  ButtonGroup
} from '@mui/material';
import {
  PhotoSizeSelectLarge,
  Schedule
} from '@mui/icons-material';

interface GallerySortControlsProps {
  sortBy: 'size' | 'date';
  onSortChange: (sortBy: 'size' | 'date') => void;
  totalItems: number;
  disabled?: boolean;
}

const GallerySortControls: React.FC<GallerySortControlsProps> = ({
  sortBy,
  onSortChange,
  totalItems,
  disabled = false
}) => {
  const isDisabled = disabled || totalItems === 0;

  return (
    <Box>
      <ButtonGroup
        variant="outlined"
        size="medium"
        disabled={isDisabled}
        sx={{
          '& .MuiButton-root': {
            textTransform: 'none',
            fontWeight: 500,
            px: 2.2,
            py: 1.5,
            borderColor: isDisabled ? 'action.disabled' : 'divider',
            borderRadius: 1,
            color: isDisabled ? 'action.disabled' : 'text.primary',
            '&:first-of-type': {
              borderTopRightRadius: 0,
              borderBottomRightRadius: 0,
            },
            '&:last-of-type': {
              borderTopLeftRadius: 0,
              borderBottomLeftRadius: 0,
            },
            '&:disabled': {
              borderColor: 'action.disabled',
              color: 'action.disabled'
            }
          }
        }}
      >
        <Button
          startIcon={<PhotoSizeSelectLarge />}
          onClick={() => !isDisabled && onSortChange('size')}
          variant={sortBy === 'size' ? 'contained' : 'outlined'}
          disabled={isDisabled}
          sx={{
            backgroundColor: sortBy === 'size' && !isDisabled ? 'primary.main' : 'transparent',
            color: isDisabled ? 'action.disabled !important' : (sortBy === 'size' ? 'white !important' : 'text.primary'),
            '&:hover': !isDisabled ? {
              backgroundColor: sortBy === 'size' ? 'primary.dark' : 'action.hover',
              borderColor: 'primary.main'
            } : {}
          }}
        >
          File Size
        </Button>

        <Button
          startIcon={<Schedule />}
          onClick={() => !isDisabled && onSortChange('date')}
          variant={sortBy === 'date' ? 'contained' : 'outlined'}
          disabled={isDisabled}
          sx={{
            backgroundColor: sortBy === 'date' && !isDisabled ? 'primary.main' : 'transparent',
            color: isDisabled ? 'action.disabled !important' : (sortBy === 'date' ? 'white !important' : 'text.primary'),
            '&:hover': !isDisabled ? {
              backgroundColor: sortBy === 'date' ? 'primary.dark' : 'action.hover',
              borderColor: 'primary.main'
            } : {}
          }}
        >
          Creation Date
        </Button>
      </ButtonGroup>
    </Box>
  );
};

export default GallerySortControls;
